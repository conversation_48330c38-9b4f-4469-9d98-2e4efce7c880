import fs from "fs";
import path from "path";
import { getDb } from "../config/mongodb.js";
import { cache } from "../config/redis.js";
import cron from "node-cron";

const DAYS_TO_FETCH = 15;

const processTokenCharts = (boughtTokens, soldTokens) => {};

const injectTokenSymbols = (chart, tokenMetadata) => {};

class SmartMoney {
  constructor() {
    this.isRunning = false;
  }

  async init() {
    this.isRunning = true;
    console.log("Initializing SmartMoney");

    // test updateData
    this.updateData();
  }

  stop() {
    this.isRunning = false;
    console.log("Stopped SmartMoney");
  }

  async startLooper() {
    cron.schedule('* * * * * *', this.updateData().bind(this));
  }

  async updateData() {
    let dbCentral = null;
    let dbJupdca = null;
    let result = null;
    try {
      dbCentral = await getDb("centraldata");
      dbJupdca = await getDb("jupdca");
      const tokensNeeded = new Set();
      const excludeCoins = new Set();

      // Get stablecoins and exclude coins
      const [stablecoinResult, solDerivatriveResult] = await Promise.all([
        dbCentral.collection("stablecoins").find().toArray(),
        dbCentral.collection("sol_derivative_tokens").find().toArray(),
      ]);

      stablecoinResult.forEach((coin) => excludeCoins.add(coin.address));
      solDerivatriveResult.forEach((coin) => excludeCoins.add(coin.address));

      // Get date range for queries
      const dateLimit = new Date();
      dateLimit.setDate(dateLimit.getDate() - DAYS_TO_FETCH);

      // Parallel fetch for all required data
      const [most_bought_tokens, most_sold_tokens, dailyFlows] =
        await Promise.all([
          dbJupdca
            .collection("daily_top_token_rankings")
            .aggregate([
              {
                $match: {
                  date: { $gte: dateLimit },
                  rank_type: "top",
                },
              },
              { $sort: { date: -1, rank: 1 } },
              {
                $group: {
                  _id: "$date",
                  tokens: { $push: "$$ROOT" },
                },
              },
              { $project: { tokens: { $slice: ["$tokens", 10] } } },
              { $unwind: "$tokens" },
              { $replaceRoot: { newRoot: "$tokens" } },
            ])
            .sort({ date: -1, rank: 1 })
            .toArray(),
          dbJupdca
            .collection("daily_bottom_token_rankings")
            .aggregate([
              {
                $match: {
                  date: { $gte: dateLimit },
                  rank_type: "bottom",
                },
              },
              { $sort: { date: -1, rank: 1 } },
              {
                $group: {
                  _id: "$date",
                  tokens: { $push: "$$ROOT" },
                },
              },
              { $project: { tokens: { $slice: ["$tokens", 10] } } },
              { $unwind: "$tokens" },
              { $replaceRoot: { newRoot: "$tokens" } },
            ])
            .sort({ date: -1, rank: 1 })
            .toArray(),
          dbJupdca
            .collection("daily_flows")
            .find({ date: { $gte: dateLimit } })
            .sort({ date: -1 })
            .toArray(),
        ]);

      // store in redis use TTL 1 hour = 3600
      await Promise.all([
        cache.set(
          "smart_money:most_bought_tokens",
          most_bought_tokens.map((item) => {
            // remove the _id field
            delete item._id;
            return item;
          }),
          3600
        ),
        cache.set(
          "smart_money:most_sold_tokens",
          most_sold_tokens.map((item) => {
            // remove the _id field
            delete item._id;
            return item;
          }),
          3600  
        ),
        cache.set(
          "smart_money:daily_flows_sol",
          dailyFlows.map((item) => {
            return {
              date: item.date,
              buy_volume: item.solFlow.buy_volume,
              sell_volume: item.solFlow.sell_volume,
              net_volume: item.solFlow.net_volume,
            };
          }),
          3600
        ),
        cache.set(
          "smart_money:daily_flows_meme",
          dailyFlows.map((item) => {
            return {
              date: item.date,
              buy_volume: item.memecoinFlow.buy_volume,
              sell_volume: item.memecoinFlow.sell_volume,
              net_volume: item.memecoinFlow.net_volume,
            };
          }),
          3600
        ),
      ]);

      // write the results to a file
      (async () => {
        const filename = `smartmoney_test.json`;
        const filepath = path.join("temp", filename);
        fs.writeFileSync(
          filepath,
          JSON.stringify(
            {
              most_bought_tokens,
              most_sold_tokens,
              dailyFlows,
            },
            null,
            2
          )
        );
        console.log(`SmartMoney test data saved to: ${filepath}`);
      })();

      // Collect token addresses for metadata lookup
      most_bought_tokens.forEach((token) =>
        tokensNeeded.add(token.token_address)
      );
      most_sold_tokens.forEach((token) =>
        tokensNeeded.add(token.token_address)
      );

      // Process chart data
      // const { mostBoughtTokenChart, mostSoldTokenChart } = processTokenCharts(
      //   most_bought_tokens,
      //   most_sold_tokens
      // );

      // const { marketSolanaChart, marketMemeChart } =
      //   processMarketCharts(dailyFlows);

      // Get token metadata
      const tokenMetadata = await dbCentral
        .collection("token_metadata")
        .find({ address: { $in: Array.from(tokensNeeded) } })
        .toArray();

      // console.log("tokenMetadata", tokenMetadata);

      // Inject token symbols into charts
      // injectTokenSymbols(mostBoughtTokenChart, tokenMetadata);
      // injectTokenSymbols(mostSoldTokenChart, tokenMetadata);

      // result = {
      //   mostBoughtTokenChart,
      //   mostSoldTokenChart,
      //   marketSolanaChart,
      //   marketMemeChart,
      //   tokenMetadata,
      // };

      return true;
    } catch (error) {
      console.error("Error in getTrendsCharts:", error);
      throw error;
    } finally {
      // Clean up variables
      dbCentral = null;
      dbJupdca = null;
    }
  }
}

export default SmartMoney;
